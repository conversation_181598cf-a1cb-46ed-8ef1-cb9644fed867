import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, CheckSquare, Smartphone, Puzzle, Clock } from "lucide-react";
import Image from "next/image";
import { AnimatedText } from "@/components/animated-text";

export default function LandingPage() {
  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Navigation Bar */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-br from-primary/5 to-secondary/10 backdrop-blur-sm border-b border-gray-200">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex items-center justify-between h-16">
            {/* Logo and App Name */}
            <div className="flex items-center gap-3">
              <Image
                src="/images/logo.png"
                alt="Make My Days Logo"
                width={32}
                height={32}
                className="h-8 w-8"
              />
              <span className="hidden md:block text-xl font-bold text-gray-900">
                Make My Days
              </span>
            </div>

            {/* Join <PERSON><PERSON> */}
            <Button className="bg-primary hover:bg-primary/90 text-white px-6 py-2">
              Join
            </Button>
          </div>
        </div>
      </nav>

      <main className="flex-1 pt-16">
        {/* Hero Section */}
        <section
          className="w-full h-full bg-gradient-to-br from-primary/5 to-secondary/10 relative pt-20 md:pt-[100px] min-h-[650px] md:min-h-[750px]"
        >
          <div className="container px-4 md:px-6 mx-auto">
            <div className="flex flex-col justify-center items-center space-y-8 text-center max-w-4xl mx-auto">
              <div className="flex items-center mb-4">
                <Image
                  src="/images/logo.png"
                  alt="Make My Days Logo"
                  width={64}
                  height={64}
                  className="h-16 w-16"
                />
                <span className="ml-3 text-3xl font-bold text-gray-900">
                  Make My Days
                </span>
              </div>

              <div className="space-y-4 max-w-2xl">
                <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-semibold tracking-tight text-primary text-left leading-tight">
                  Plan your days in
                  <br />
                  the ADHD way
                </h1>
              </div>

              <div className="space-y-4 max-w-2xl">
                <p className="text-lg md:text-xl text-primary leading-relaxed font-medium">
                  An AI-enabled time orchestrator that revitalizes your
                  reschedule according to reality without any pain.
                </p>
              </div>

              <div className="flex justify-center">
                <Button className="bg-primary hover:bg-primary/90 text-white text-xl px-12 py-4 m-px">
                  Join Waitlist
                </Button>
              </div>
            </div>

            {/* Bottom tagline */}
            <div className="absolute bottom-5 left-1/2 transform -translate-x-1/2">
              <p className="text-base md:text-lg text-primary font-medium">
                Developed by ADHDers, for ADHDers.
              </p>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="w-full py-16 md:py-24">
          <div className="container px-4 md:px-6 mx-auto max-w-[880px]">
            <div className="space-y-32">
              {/* Feature 1: Planning with AI */}
              <div className="space-y-16">
                {/* Problem Statement */}
                <div className="text-center py-16 md:py-24">
                  <AnimatedText>
                    <h3 className="text-4xl md:text-5xl lg:text-6xl font-bold text-primary">
                      You can never make realistic plans?
                    </h3>
                  </AnimatedText>
                </div>

                {/* Feature Section */}
                <AnimatedText>
                  <div className="bg-[#F6F5F4] rounded-xl p-8 md:p-12 relative overflow-hidden">
                    <div className="space-y-6">
                      {/* Feature Title with Logo */}
                      <h4 className="text-2xl md:text-3xl font-bold text-gray-900">
                        <Image
                          src="/images/logo.png"
                          alt="Make My Days Logo"
                          width={32}
                          height={32}
                          className="h-8 w-8 inline mr-3 align-text-top"
                        />
                        Make My Days plans with you
                      </h4>

                      {/* Feature Description */}
                      <div className="space-y-4">
                        <p className="text-lg text-gray-400 leading-relaxed">
                          Make My Days’s AI helps you to make long-term plans
                          that you can stick to. the AI will even{" "}
                          <span className="text-gray-900">
                            ask you to rethink when you propose too-optimistic
                            plans.
                          </span>
                        </p>
                      </div>

                      {/* Feature Image */}
                      <div className="mt-8 text-center">
                        <div className="relative">
                          <Image
                            src="/images/plan_realistically.png"
                            width={483}
                            height={525}
                            alt="AI helping user plan realistic wake-up time goals"
                            className="rounded-t-lg mx-auto"
                            style={{ marginBottom: "-2.9rem" }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedText>
              </div>

              {/* Feature 2: Sub-tasking by AI */}
              <div className="space-y-16">
                {/* Problem Statement */}
                <div className="text-center py-16 md:py-24">
                  <AnimatedText>
                    <h3 className="text-4xl md:text-5xl lg:text-6xl font-bold text-primary">
                      Your mind wanders and you forget what to do next?
                    </h3>
                  </AnimatedText>
                </div>

                {/* Feature Section */}
                <AnimatedText>
                  <div className="bg-[#F6F5F4] rounded-xl p-8 md:p-12 relative overflow-hidden">
                    <div className="space-y-6">
                      {/* Feature Title with Logo */}
                      <h4 className="text-2xl md:text-3xl font-bold text-gray-900">
                        <Image
                          src="/images/logo.png"
                          alt="Make My Days Logo"
                          width={32}
                          height={32}
                          className="h-8 w-8 inline mr-3 align-text-top"
                        />
                        Make My Days turns your thoughts to achievable steps
                      </h4>

                      {/* Feature Description */}
                      <div className="space-y-4">
                        <p className="text-lg text-gray-400 leading-relaxed">
                          Make My Days’s AI turns your general thoughts of
                          "doing something" into{" "}
                          <span className="text-gray-900">
                            detailed action items
                          </span>{" "}
                          and all you need is to confirm and check each of them
                          after finishing.
                        </p>
                      </div>

                      {/* Feature Image */}
                      <div className="mt-8 text-center">
                        <div className="relative">
                          <Image
                            src="/images/achievable_steps.png"
                            width={460}
                            height={528}
                            alt="Task breakdown from general idea to specific steps"
                            className="rounded-t-lg mx-auto"
                            style={{ marginBottom: "-2.9rem" }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedText>
              </div>

              {/* Feature 3: Dynamic Schedule by AI */}
              <div className="space-y-16">
                {/* Problem Statement */}
                <div className="text-center py-16 md:py-24">
                  <AnimatedText>
                    <h3 className="text-4xl md:text-5xl lg:text-6xl font-bold text-primary">
                      You miss deadlines and blame yourself for that?
                    </h3>
                  </AnimatedText>
                </div>

                {/* Feature Section */}
                <AnimatedText>
                  <div className="bg-[#F6F5F4] rounded-xl p-8 md:p-12 relative overflow-hidden">
                    <div className="space-y-6">
                      {/* Feature Title with Logo */}
                      <h4 className="text-2xl md:text-3xl font-bold text-gray-900">
                        <Image
                          src="/images/logo.png"
                          alt="Make My Days Logo"
                          width={32}
                          height={32}
                          className="h-8 w-8 inline mr-3 align-text-top"
                        />
                        Make My Days reschedules your todos
                      </h4>

                      {/* Feature Description */}
                      <div className="space-y-4">
                        <p className="text-lg text-gray-400 leading-relaxed">
                          Make My Days's AI can reschedule your entire day
                          without any stress or guilt. When life happens and
                          plans change, our AI{" "}
                          <span className="text-gray-900">
                            instantly adapts your schedule to reality,
                          </span>{" "}
                          keeping you on track without the mental overhead of
                          replanning everything yourself.
                        </p>
                      </div>

                      {/* Feature Image */}
                      <div className="mt-8 text-center">
                        <div className="relative">
                          <Image
                            src="/images/reschedule.png"
                            width={451}
                            height={434}
                            alt="AI rescheduling task with smart suggestions"
                            className="rounded-t-lg mx-auto"
                            style={{ marginBottom: "-2.9rem" }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedText>
              </div>

              {/* Feature 4: sync to any calendar */}
              <div className="space-y-16">
                {/* Problem Statement */}
                <div className="text-center py-16 md:py-24">
                  <AnimatedText>
                    <h3 className="text-4xl md:text-5xl lg:text-6xl font-bold text-primary">
                      You forgot to check off you todo lists?
                    </h3>
                  </AnimatedText>
                </div>

                {/* Feature Section */}
                <AnimatedText>
                  <div className="bg-[#F6F5F4] rounded-xl p-8 md:p-12 relative overflow-hidden">
                    <div className="space-y-6">
                      {/* Feature Title with Logo */}
                      <h4 className="text-2xl md:text-3xl font-bold text-gray-900">
                        <Image
                          src="/images/logo.png"
                          alt="Make My Days Logo"
                          width={32}
                          height={32}
                          className="h-8 w-8 inline mr-3 align-text-top"
                        />
                        Make My Days syncs to any of your calendars
                      </h4>

                      {/* Feature Description */}
                      <div className="space-y-4">
                        <p className="text-lg text-gray-400 leading-relaxed">
                          Make My Days's supports iCalendar, the standard
                          calendar format, and it syncs to any of your digital
                          calendars,
                          <span className="text-gray-900">
                            {" "}
                            Apple Calendar, Google Calendar, and others!
                          </span>
                        </p>
                      </div>

                      {/* Feature Image */}
                      <div className="mt-8 text-center">
                        <div className="relative">
                          <Image
                            src="/images/iphone.png"
                            width={451}
                            height={434}
                            alt="the app works on iPhone"
                            className="rounded-t-lg mx-auto"
                            style={{ marginBottom: "-2.9rem" }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedText>
              </div>
            </div>
          </div>
        </section>

        {/* Q&A Section */}
        <section className="w-full py-16 md:py-24 bg-white">
          <div className="container px-4 md:px-6 mx-auto max-w-[880px]">
            <div className="space-y-12">
              {/* Section Title */}
              <div className="text-center">
                <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
                  Frequently Asked Questions
                </h2>
              </div>

              {/* Q&A Item */}
              <div className="space-y-8">
                <div className="bg-gray-50 rounded-xl p-8">
                  <div className="space-y-4">
                    <h3 className="text-xl md:text-2xl font-semibold text-gray-900">
                      Do I need to be ADHD to use Make My Days?
                    </h3>
                    <p className="text-lg text-gray-400 leading-relaxed">
                      <span className="text-gray-900">No, absolutely not!</span>{" "}
                      Make My Days is a tool designed for people with ADHD,
                      which means it is designed to solve problems for "limited
                      working memory", "time blindness", "dysfunction of
                      executive", and other ADHD traits. It can greatly benefit
                      people who have those traits and people who want to take
                      control of their time in general.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section
          id="waitlist"
          className="w-full py-10 md:py-20 bg-gradient-to-r from-primary to-primary/80"
        >
          <div className="container px-4 md:px-6 mx-auto">
            <div className="flex flex-col items-center space-y-8 text-center max-w-2xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-white">
                Ready to Make Your Days?
              </h2>
              <p className="text-lg text-white/90">
                Join thousands of ADHDers who are taking control of their time
              </p>

              <div className="flex justify-center">
                <Button className="bg-white text-primary hover:bg-gray-100 text-base px-10 py-4 m-px">
                  Join Waitlist
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="w-full py-8 bg-gradient-to-r from-primary to-primary/80">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="text-center space-y-4">
            <div className="w-full h-px bg-white/20"></div>
            <p className="text-sm text-white">
              © 2025 Make My Days. All rights reserved. Made with ❤️ by
              ADHDers, for ADHDers.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
